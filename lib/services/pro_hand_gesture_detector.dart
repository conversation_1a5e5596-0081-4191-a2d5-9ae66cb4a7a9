// lib/services/pro_hand_gesture_detector.dart
import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart' show CameraController; // for the nullable stub




enum GestureType { rock, paper, scissors, none, unknown }




class ProHandGestureDetector with ChangeNotifier {
static const String MODE_TRAINING = "training";
static const String MODE_GAME = "game";




static const MethodChannel _methods = MethodChannel('mp_gesture/methods');
static const EventChannel _events = EventChannel('mp_gesture/events');

// Stream subscription for cleanup
StreamSubscription<dynamic>? _eventSubscription;

final _gestureCtrl = StreamController<GestureType>.broadcast();
Stream<GestureType>? get gestureStream => _gestureCtrl.stream;




GestureType _currentGesture = GestureType.none;
double _currentConfidence = 0.0;
String _currentMode = MODE_GAME;
String _detectedHand = "Unknown"; // "Left", "Right", ou "Unknown"




// Align with app expectations
GestureType get currentGesture => _currentGesture;       // enum
String get currentGestureString => _currentGesture.name; // helper for UI
double get currentConfidence => _currentConfidence;
String get currentMode => _currentMode;
String get detectedHand => _detectedHand;                // main détectée automatiquement




// Some code references a cameraController; expose a nullable stub
CameraController? get cameraController => null;




bool _initialized = false;
bool get isInitialized => _initialized;




Future<bool> initialize() async {
  if (_initialized) return true;

  debugPrint('🎮 CAMERA: Initializing ProHandGestureDetector...');
  debugPrint('🎮 DEVICE: Running on ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');

  try {

    _eventSubscription = _events.receiveBroadcastStream().listen((dynamic e) {
      if (e is Map) {
        final label = (e['label'] as String?) ?? 'none';
        final score = (e['score'] as num?)?.toDouble() ?? 0.0;
        final hand = (e['hand'] as String?) ?? 'Unknown';

        _currentGesture = _mapLabel(label);
        _currentConfidence = score;
        _detectedHand = hand;

        // Log pour debug
        debugPrint('🎯 Geste: $label, Score: $score, Main: $hand');

        _gestureCtrl.add(_currentGesture);
        notifyListeners();
      }
    }, onError: (err) {
      debugPrint('🔴 mp_gesture/events error: $err');
    });

    _initialized = true;
    debugPrint('✅ CAMERA: ProHandGestureDetector initialized successfully');
    return true;
  } catch (e) {
    debugPrint('🔴 CAMERA: Failed to initialize ProHandGestureDetector: $e');
    // En cas d'erreur, on considère comme initialisé pour éviter le crash
    _initialized = true;
    return true;
  }
}




Future<void> startDetection({String mode = MODE_GAME, double minScore = 0.65, bool useFrontCamera = true}) async {
  _currentMode = mode;
  debugPrint('🎮 CAMERA: Starting detection with mode: $mode, minScore: $minScore, useFrontCamera: $useFrontCamera');
  debugPrint('🎮 DEVICE: Running on ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');

  // Check if we're running on an emulator
  bool isEmulator = await _isRunningOnEmulator();
  if (isEmulator) {
    debugPrint('⚠️ CAMERA: Detected emulator environment - MediaPipe may not work properly');
    debugPrint('💡 CAMERA: For best results, test on a physical device with a real camera');
  }

  try {
    await _methods.invokeMethod('start', <String, dynamic>{
      'minScore': minScore,
      'useFrontCamera': useFrontCamera
    });
    debugPrint('✅ CAMERA: Detection started successfully');
  } catch (e) {
    debugPrint('🔴 CAMERA: startDetection error: $e');
    debugPrint('🔴 CAMERA: Error type: ${e.runtimeType}');
    if (e is PlatformException) {
      debugPrint('🔴 CAMERA: Platform error code: ${e.code}, message: ${e.message}');

      // Check for GPU memory issues
      if (e.message?.contains('GL_OUT_OF_MEMORY') == true ||
          e.message?.contains('ErrorOutOfDeviceMemory') == true ||
          e.message?.contains('Out of memory') == true) {
        debugPrint('🚨 CAMERA: GPU memory exhausted! Device may need restart or memory cleanup');
        debugPrint('💡 CAMERA: Try closing other apps or restarting the device');
      }
    }

    // If we're on an emulator, provide helpful guidance
    if (isEmulator) {
      debugPrint('💡 CAMERA: This error is likely due to emulator limitations');
      debugPrint('💡 CAMERA: Try testing on a physical device for proper gesture detection');
    }

    // Re-throw the error so the caller can handle it
    rethrow;
  }
}




Future<void> stopDetection() async {
  try {
    if (_methods != null) {
      await _methods.invokeMethod('stop');
    }
  } catch (e) {
    debugPrint('stopDetection error: $e');
  }
}

/// Restart MediaPipe resources - call this periodically to prevent memory leaks
Future<void> restartNativeResources() async {
  debugPrint('🔄 GESTURE_DETECTOR: Restarting native MediaPipe resources...');
  try {
    await _methods.invokeMethod('restart');
    debugPrint('✅ GESTURE_DETECTOR: Native resources restarted successfully');
  } catch (e) {
    debugPrint('🔴 GESTURE_DETECTOR: Error restarting native resources: $e');
  }
}

/// Dispose MediaPipe native resources - call this ONLY when app is closing
Future<void> disposeNativeResources() async {
  debugPrint('🧹 GESTURE_DETECTOR: Disposing native MediaPipe resources...');
  try {
    await _methods.invokeMethod('dispose');
    debugPrint('✅ GESTURE_DETECTOR: Native resources disposed successfully');
  } catch (e) {
    debugPrint('🔴 GESTURE_DETECTOR: Error disposing native resources: $e');
  }
}




String getGestureString() => _currentGesture.name; // backward-compat helper
GestureType getGesture() => _currentGesture;       // if some code expects enum




@override
void dispose() {
  debugPrint('🧹 GESTURE_DETECTOR: Disposing ProHandGestureDetector...');

  // Cancel event subscription to prevent memory leak
  _eventSubscription?.cancel();
  _eventSubscription = null;

  // Close gesture stream controller
  _gestureCtrl.close();

  debugPrint('✅ GESTURE_DETECTOR: ProHandGestureDetector disposed');
  super.dispose();
}




/// Check if we're running on an emulator
Future<bool> _isRunningOnEmulator() async {
  try {
    // Check common emulator indicators
    if (Platform.isAndroid) {
      // Android emulator detection
      final result = await _methods.invokeMethod('isEmulator');
      if (result is bool) return result;

      // Fallback: check device model/brand
      final deviceInfo = Platform.operatingSystemVersion;
      return deviceInfo.toLowerCase().contains('emulator') ||
             deviceInfo.toLowerCase().contains('simulator') ||
             deviceInfo.toLowerCase().contains('sdk');
    } else if (Platform.isIOS) {
      // iOS simulator detection
      return Platform.operatingSystemVersion.toLowerCase().contains('simulator');
    }
  } catch (e) {
    debugPrint('🔴 CAMERA: Error checking emulator status: $e');
  }

  // Default to false if we can't determine
  return false;
}

GestureType _mapLabel(String label) {
  final l = label.toLowerCase();
  if (l.contains('rock') || l.contains('closed_fist')) return GestureType.rock;
  if (l.contains('paper') || l.contains('open_palm')) return GestureType.paper;
  if (l.contains('scissors') || l.contains('victory')) return GestureType.scissors;
  if (l.contains('none') || l.isEmpty) return GestureType.none;
  return GestureType.unknown;
}

}










